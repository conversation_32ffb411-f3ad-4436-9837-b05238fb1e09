import { BollingerRSIStrategy } from '../main/strategies/BollingerRSIStrategy'

/**
 * Test the Bollinger Bands + RSI Strategy
 * This test verifies that the strategy correctly:
 * 1. Calculates Bollinger Bands and RSI indicators
 * 2. Generates appropriate buy/sell signals
 * 3. Applies M1 optimizations and filters
 * 4. Returns proper TradingDecision objects
 */

// Mock candle data for testing
const generateMockCandles = (count: number, basePrice: number = 1.0): Candle[] => {
  const candles: Candle[] = []
  let price = basePrice

  for (let i = 0; i < count; i++) {
    // Simulate some price movement
    const change = (Math.random() - 0.5) * 0.0002 // Small changes for forex-like data
    price += change

    const high = price + Math.random() * 0.0001
    const low = price - Math.random() * 0.0001
    const open = i === 0 ? price : candles[i - 1].close

    candles.push({
      time: Date.now() + i * 60000, // 1 minute intervals
      open,
      high,
      low,
      close: price
    })
  }

  return candles
}

// Generate test scenario with oversold condition
const generateOversoldScenario = (): Candle[] => {
  const candles: Candle[] = []
  let price = 1.0

  // Generate 40 candles with strong declining trend to create oversold RSI
  for (let i = 0; i < 40; i++) {
    let change = 0
    if (i < 25) {
      // Strong decline for first 25 candles to push RSI below 30
      change = -0.0002 - Math.random() * 0.0001
    } else if (i < 35) {
      // Continue decline but slower
      change = -0.00005 - Math.random() * 0.00005
    } else {
      // Final candles: price touches lower Bollinger Band
      change = -0.00001
    }

    price += change
    const open = i === 0 ? price : candles[i - 1].close

    candles.push({
      time: Date.now() + i * 60000,
      open,
      high: price + Math.abs(change) * 0.2,
      low: price - Math.abs(change) * 0.3, // Lower lows to strengthen downtrend
      close: price
    })
  }

  return candles
}

// Generate test scenario with overbought condition
const generateOverboughtScenario = (): Candle[] => {
  const candles: Candle[] = []
  let price = 1.0

  // Generate 40 candles with strong rising trend to create overbought RSI
  for (let i = 0; i < 40; i++) {
    let change = 0
    if (i < 25) {
      // Strong rise for first 25 candles to push RSI above 70
      change = 0.0002 + Math.random() * 0.0001
    } else if (i < 35) {
      // Continue rise but slower
      change = 0.00005 + Math.random() * 0.00005
    } else {
      // Final candles: price touches upper Bollinger Band
      change = 0.00001
    }

    price += change
    const open = i === 0 ? price : candles[i - 1].close

    candles.push({
      time: Date.now() + i * 60000,
      open,
      high: price + Math.abs(change) * 0.3, // Higher highs to strengthen uptrend
      low: price - Math.abs(change) * 0.2,
      close: price
    })
  }

  return candles
}

async function testBollingerRSIStrategy() {
  console.log('🧪 Testing Bollinger Bands + RSI Strategy...\n')

  // Create strategy instance with M1 optimized configuration
  const strategy = new BollingerRSIStrategy({
    minConfidence: 0.5,
    expiryToSeconds: 60,
    candlePeriodSeconds: 60,
    bollingerRsiConfig: {
      bollingerPeriod: 20,
      bollingerStdDev: 2.0,
      rsiPeriod: 14,
      rsiOverbought: 70,
      rsiOversold: 30,
      noiseFilter: true,
      trendConfirmation: true,
      volatilityThreshold: 0.005
    }
  })

  console.log(`📊 Strategy: ${strategy.getName()}`)
  console.log(`📝 Description: ${strategy.getDescription()}\n`)

  // Test 1: Insufficient data
  console.log('Test 1: Insufficient data scenario')
  const insufficientCandles = generateMockCandles(10)
  for (const candle of insufficientCandles) {
    strategy.addCandle(candle)
  }

  const decision1 = await strategy.evaluate(insufficientCandles[insufficientCandles.length - 1])
  console.log(`Result: ${decision1.shouldTrade ? 'TRADE' : 'NO TRADE'} - ${decision1.reason}`)
  console.log(`Confidence: ${(decision1.confidence || 0) * 100}%\n`)

  // Test 2: Normal market conditions
  console.log('Test 2: Normal market conditions')
  const normalCandles = generateMockCandles(50)

  // Reset strategy for clean test
  const strategy2 = new BollingerRSIStrategy({
    minConfidence: 0.5,
    expiryToSeconds: 60,
    candlePeriodSeconds: 60,
    bollingerRsiConfig: {
      bollingerPeriod: 20,
      bollingerStdDev: 2.0,
      rsiPeriod: 14,
      rsiOverbought: 70,
      rsiOversold: 30,
      noiseFilter: true,
      trendConfirmation: true,
      volatilityThreshold: 0.005
    }
  })

  for (const candle of normalCandles) {
    strategy2.addCandle(candle)
  }

  const decision2 = await strategy2.evaluate(normalCandles[normalCandles.length - 1])
  console.log(`Result: ${decision2.shouldTrade ? 'TRADE' : 'NO TRADE'} - ${decision2.reason}`)
  console.log(`Direction: ${decision2.direction || 'N/A'}`)
  console.log(`Confidence: ${(decision2.confidence || 0) * 100}%`)
  console.log(`Expiry: ${decision2.expirySeconds || 'N/A'} seconds`)
  if (decision2.metadata) {
    console.log(`Metadata:`, decision2.metadata)
  }
  console.log()

  // Test 3: Oversold scenario (should generate BUY signal)
  console.log('Test 3: Oversold scenario (expecting BUY signal)')
  const oversoldCandles = generateOversoldScenario()

  const strategy3 = new BollingerRSIStrategy({
    minConfidence: 0.4, // Lower threshold for testing
    expiryToSeconds: 60,
    candlePeriodSeconds: 60,
    bollingerRsiConfig: {
      bollingerPeriod: 20,
      bollingerStdDev: 2.0,
      rsiPeriod: 14,
      rsiOverbought: 70,
      rsiOversold: 30,
      noiseFilter: false, // Disable for testing
      trendConfirmation: false,
      volatilityThreshold: 0.01
    }
  })

  for (const candle of oversoldCandles) {
    strategy3.addCandle(candle)
  }

  const decision3 = await strategy3.evaluate(oversoldCandles[oversoldCandles.length - 1])
  console.log(`Result: ${decision3.shouldTrade ? 'TRADE' : 'NO TRADE'} - ${decision3.reason}`)
  console.log(`Direction: ${decision3.direction || 'N/A'}`)
  console.log(`Confidence: ${(decision3.confidence || 0) * 100}%`)
  if (decision3.metadata) {
    console.log(`Metadata:`, decision3.metadata)
  }
  console.log()

  // Test 4: Overbought scenario (should generate SELL signal)
  console.log('Test 4: Overbought scenario (expecting SELL signal)')
  const overboughtCandles = generateOverboughtScenario()

  const strategy4 = new BollingerRSIStrategy({
    minConfidence: 0.4, // Lower threshold for testing
    expiryToSeconds: 60,
    candlePeriodSeconds: 60,
    bollingerRsiConfig: {
      bollingerPeriod: 20,
      bollingerStdDev: 2.0,
      rsiPeriod: 14,
      rsiOverbought: 70,
      rsiOversold: 30,
      noiseFilter: false, // Disable for testing
      trendConfirmation: false,
      volatilityThreshold: 0.01
    }
  })

  for (const candle of overboughtCandles) {
    strategy4.addCandle(candle)
  }

  const decision4 = await strategy4.evaluate(overboughtCandles[overboughtCandles.length - 1])
  console.log(`Result: ${decision4.shouldTrade ? 'TRADE' : 'NO TRADE'} - ${decision4.reason}`)
  console.log(`Direction: ${decision4.direction || 'N/A'}`)
  console.log(`Confidence: ${(decision4.confidence || 0) * 100}%`)
  if (decision4.metadata) {
    console.log(`Metadata:`, decision4.metadata)
  }
  console.log()

  console.log('✅ Bollinger Bands + RSI Strategy tests completed!')
}

// Run the test if this file is executed directly
if (require.main === module) {
  testBollingerRSIStrategy().catch(console.error)
}

export { testBollingerRSIStrategy }
