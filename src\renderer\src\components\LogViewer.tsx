import { useEffect, useRef, useState, useCallback } from 'react'

interface LogEntry {
  type: string
  time: string
  message: string
  color: string
}

interface LogConfig {
  type: string
  time?: Date
}

export default function LogViewer(): React.JSX.Element {
  const logContainerRef = useRef<HTMLDivElement>(null)
  const [isUserScrolling, setIsUserScrolling] = useState(false)
  const [logEntries, setLogEntries] = useState<LogEntry[]>([
    // {
    //   type: 'info',
    //   time: '12:34:56',
    //   message: 'Bot connected successfully',
    //   icon: 'ℹ',
    //   color: 'text-blue-400'
    // },
    // {
    //   type: 'success',
    //   time: '12:35:12',
    //   message: 'Trade executed: BUY $25.00',
    //   icon: '✓',
    //   color: 'text-green-400'
    // },
    // {
    //   type: 'warning',
    //   time: '12:35:45',
    //   message: 'Low balance warning: $75.00 remaining',
    //   icon: '⚠',
    //   color: 'text-yellow-400'
    // },
    // {
    //   type: 'error',
    //   time: '12:36:01',
    //   message: 'Connection timeout - retrying...',
    //   icon: '✗',
    //   color: 'text-red-400'
    // },
    // {
    //   type: 'debug',
    //   time: '12:36:15',
    //   message: 'Strategy evaluation: confidence 0.85',
    //   icon: '🔧',
    //   color: 'text-purple-400'
    // },
    // {
    //   type: 'trade',
    //   time: '12:37:30',
    //   message: 'Trade won: +$21.25 profit',
    //   icon: '💰',
    //   color: 'text-blue-400'
    // },
    // {
    //   type: 'system',
    //   time: '12:38:00',
    //   message: 'Money management: next trade $26.25',
    //   icon: '⚙',
    //   color: 'text-gray-400'
    // },
    // {
    //   type: 'info',
    //   time: '12:38:30',
    //   message: 'Market analysis complete',
    //   icon: 'ℹ',
    //   color: 'text-blue-400'
    // },
    // {
    //   type: 'warning',
    //   time: '12:39:15',
    //   message: 'High volatility detected',
    //   icon: '⚠',
    //   color: 'text-yellow-400'
    // },
    // {
    //   type: 'success',
    //   time: '12:40:00',
    //   message: 'Signal confirmed: SELL opportunity',
    //   icon: '✓',
    //   color: 'text-green-400'
    // }
  ])

  // Check if user is at the bottom of the scroll container
  const isAtBottom = (): boolean => {
    if (!logContainerRef.current) return false
    const { scrollTop, scrollHeight, clientHeight } = logContainerRef.current
    return Math.abs(scrollHeight - clientHeight - scrollTop) < 5 // 5px tolerance
  }

  // Scroll to bottom manually
  const scrollToBottom = useCallback((): void => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight
      setIsUserScrolling(false)
    }
  }, [])

  // Handle scroll events to detect user scrolling
  const handleScroll = useCallback((): void => {
    if (isAtBottom()) {
      setIsUserScrolling(false)
    } else {
      setIsUserScrolling(true)
    }
  }, [])

  // Auto-scroll to bottom when log entries change (only if user hasn't scrolled up)
  useEffect(() => {
    if (logContainerRef.current && !isUserScrolling) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight
    }
  }, [logEntries.length, isUserScrolling])

  // Scroll to bottom on component mount
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight
    }
  }, [])

  // Add scroll event listener
  useEffect(() => {
    const container = logContainerRef.current
    if (container) {
      container.addEventListener('scroll', handleScroll)
      return () => container.removeEventListener('scroll', handleScroll)
    }
    return undefined
  }, [handleScroll])

  useEffect(() => {
    const unsub = window.api.on('log:event', (...args: unknown[]) => {
      const [message, config] = args

      // Type guard to ensure we have the expected structure
      if (
        typeof message === 'string' &&
        config &&
        typeof config === 'object' &&
        config !== null &&
        'type' in config &&
        typeof (config as LogConfig).type === 'string'
      ) {
        const logConfig = config as LogConfig

        setLogEntries((prev) => [
          ...prev,
          {
            type: logConfig.type,
            time: formatTime(logConfig.time),
            message: message,
            color: getTextColor(logConfig.type)
          }
        ])
      }
    })

    return () => {
      unsub()
    }
  }, [])

  return (
    <div className="flex flex-col w-full min-w-[380px] bg-gray-800/80 p-1 px-2 sm:p-2 sm:px-3 rounded-sm shadow-md relative">
      <h3 className="text-white text-xs sm:text-sm mb-1 font-bold">📜 Log Viewer</h3>
      <div
        ref={logContainerRef}
        className="flex flex-col gap-[0.05rem] bg-gray-900 h-[9rem] sm:h-[10rem] md:h-[11rem] rounded-sm w-full custom-scrollbar overflow-y-auto scroll-smooth"
      >
        {logEntries.map((entry, index) => {
          return (
            <div
              key={index}
              className="flex items-start px-2 py-0.5 sm:px-3 sm:py-1 hover:bg-gray-800/50 transition-colors"
            >
              {/* <span className={`${entry.color} text-xs mr-1 sm:mr-2 font-mono`}>{entry.icon}</span> */}
              <div className="flex items-center flex-1">
                <span className="text-gray-400 text-[10px] sm:text-xs mr-1 sm:mr-2 font-mono">
                  {entry.time}
                </span>
                <div
                  className={`${getTextColor(entry.type)} text-xs leading-tight sm:leading-normal`}
                  dangerouslySetInnerHTML={{ __html: entry.message }}
                />
              </div>
            </div>
          )
        })}
      </div>

      {/* Scroll to bottom button - only visible when user has scrolled up */}
      {isUserScrolling && (
        <button
          onClick={scrollToBottom}
          className="absolute bottom-2 right-2 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-full p-1 shadow-md transition-all opacity-80 hover:opacity-100"
          title="Scroll to latest logs"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      )}
    </div>
  )
}

function formatTime(date = new Date()): string {
  const pad = (n: number): string => String(n).padStart(2, '0')
  const hh = pad(date.getHours())
  const mm = pad(date.getMinutes())
  const ss = pad(date.getSeconds())
  return `${hh}:${mm}:${ss}`
}

function getTextColor(type: string): string {
  switch (type) {
    case 'success':
    case 'trade':
      return 'text-green-300'
    case 'warning':
      return 'text-yellow-300'
    case 'error':
      return 'text-red-300'
    case 'debug':
    case 'system':
      return 'text-gray-300'
    default:
      return 'text-white'
  }
}
