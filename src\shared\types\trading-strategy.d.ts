interface StrategyConfig {
  cooldownPeriod?: number
  minConfidence?: number
  expiryToSeconds?: number // User-defined expiry
  candlePeriodSeconds?: number // Chart period

  // ATR-scaled threshold
  atrPeriod?: number // default 14
  atrMultiplier?: number // e.g. 1.0–8.0
  minThresholdPct?: number // floor threshold, e.g. 0.002 for 0.2%
  expirySeconds?: number // optional override for expiry

  // Threshold Strategy
  threshold?: number
  maxVolatilityPercent?: number
  volatilityFilter?: boolean
  momentumConfirmation?: boolean
  consistencyCheck?: boolean

  // SuperTrend Strategy
  superTrends?: { atrPeriod: number; multiplier: number }[]

  // Back2Trend Strategy
  back2TrendConfig?: { lookbackPeriod?: number; atrMultiplier?: number; slopeThreshold?: number }

  // Bollinger Bands + RSI Strategy
  bollingerRsiConfig?: {
    bollingerPeriod?: number // default 20
    bollingerStdDev?: number // default 2.0
    rsiPeriod?: number // default 14
    rsiOverbought?: number // default 70
    rsiOversold?: number // default 30
    noiseFilter?: boolean // default true for M1 optimization
    trendConfirmation?: boolean // default true
    volatilityThreshold?: number // default 0.5% for M1
  }
}

interface PriceData {
  current: number
  previous: number
  timestamp: number
  trend: 'up' | 'down' | 'neutral'
  change: number
  changePercent: number
}

interface TradingDecision {
  shouldTrade: boolean
  direction?: 'high' | 'low'
  confidence?: number // 0-1 scale
  reason?: string
  metadata?: unknown // Additional strategy-specific data
  expirySeconds?: number
}

interface RSIResult {
  rsi: number
  trend: 'overbought' | 'oversold' | 'neutral'
}
