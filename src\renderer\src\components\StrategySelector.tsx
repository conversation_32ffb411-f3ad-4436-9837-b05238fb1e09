import { useState, useEffect } from 'react'

interface AvailableStrategy {
  type: StrategyType
  name: string
  description: string
}

interface StrategyConfig {
  minConfidence?: number
  candlePeriodSeconds?: number
  expiryToSeconds?: number

  // Threshold Strategy
  threshold?: number
  maxVolatilityPercent?: number
  volatilityFilter?: boolean
  momentumConfirmation?: boolean
  consistencyCheck?: boolean

  // SuperTrend Strategy
  superTrends?: { atrPeriod: number; multiplier: number }[]

  // Back2Trend Strategy
  back2TrendConfig?: { lookbackPeriod?: number; atrMultiplier?: number; slopeThreshold?: number }

  // Bollinger Bands + RSI Strategy
  bollingerRsiConfig?: {
    bollingerPeriod?: number
    bollingerStdDev?: number
    rsiPeriod?: number
    rsiOverbought?: number
    rsiOversold?: number
    noiseFilter?: boolean
    trendConfirmation?: boolean
    volatilityThreshold?: number
  }
}

interface StrategySelectorProps {
  isOpen: boolean
  onClose: () => void
}

const StrategySelector: React.FC<StrategySelectorProps> = ({ isOpen, onClose }) => {
  const [availableStrategies, setAvailableStrategies] = useState<AvailableStrategy[]>([])
  const [activeStrategies, setActiveStrategies] = useState<StrategyType[]>([])
  const [strategyConfigs, setStrategyConfigs] = useState<
    Partial<Record<StrategyType, StrategyConfig>>
  >({})
  const [selectedStrategy, setSelectedStrategy] = useState<StrategyType | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load initial data
  useEffect(() => {
    if (isOpen) {
      loadStrategyData()
    }
  }, [isOpen])

  const loadStrategyData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const [available, active, configs] = await Promise.all([
        window.api.invoke('strategy:getAvailable') as Promise<AvailableStrategy[]>,
        window.api.invoke('strategy:getActive') as Promise<StrategyType[]>,
        window.api.invoke('strategy:getConfigs') as Promise<
          Partial<Record<StrategyType, StrategyConfig>>
        >
      ])

      setAvailableStrategies(available)
      setActiveStrategies(active)
      setStrategyConfigs(configs)

      if (active.length > 0) {
        setSelectedStrategy(active[0])
      }
    } catch (err) {
      console.error('Failed to load strategy data:', err)
      setError('Failed to load strategy data. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleStrategyToggle = async (strategyType: StrategyType) => {
    try {
      setError(null)

      let newActiveStrategies: StrategyType[]

      if (activeStrategies.includes(strategyType)) {
        // Remove strategy
        newActiveStrategies = activeStrategies.filter((s) => s !== strategyType)
      } else {
        // Add strategy
        newActiveStrategies = [...activeStrategies, strategyType]

        // Load default config if not exists
        if (!strategyConfigs[strategyType]) {
          const defaultConfig = (await window.api.invoke(
            'strategy:getDefaultConfig',
            strategyType
          )) as StrategyConfig
          setStrategyConfigs((prev) => ({
            ...prev,
            [strategyType]: defaultConfig
          }))
        }
      }

      await (window.api.invoke as any)(
        'strategy:updateActive',
        newActiveStrategies,
        strategyConfigs
      )
      setActiveStrategies(newActiveStrategies)

      // Select first active strategy if current selection was removed
      if (!newActiveStrategies.includes(selectedStrategy!) && newActiveStrategies.length > 0) {
        setSelectedStrategy(newActiveStrategies[0])
      }
    } catch (err) {
      console.error('Failed to update strategies:', err)
      setError('Failed to update strategies. Please try again.')
    }
  }

  const handleConfigUpdate = async (
    strategyType: StrategyType,
    configUpdate: Partial<StrategyConfig>
  ) => {
    try {
      setError(null)

      const newConfig = {
        ...strategyConfigs[strategyType],
        ...configUpdate
      }

      await (window.api.invoke as any)('strategy:updateConfig', strategyType, configUpdate)

      setStrategyConfigs((prev) => ({
        ...prev,
        [strategyType]: newConfig
      }))
    } catch (err) {
      console.error('Failed to update strategy config:', err)
      setError('Failed to update strategy configuration. Please try again.')
    }
  }

  const renderStrategyConfig = (strategyType: StrategyType) => {
    const config = strategyConfigs[strategyType]
    if (!config) return null

    switch (strategyType) {
      case 'bollinger-rsi':
        return (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-300">
              Bollinger Bands + RSI Configuration
            </h4>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs text-gray-400 mb-1">Bollinger Period</label>
                <input
                  type="number"
                  value={config.bollingerRsiConfig?.bollingerPeriod || 20}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      bollingerRsiConfig: {
                        ...config.bollingerRsiConfig,
                        bollingerPeriod: parseInt(e.target.value)
                      }
                    })
                  }
                  className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-xs"
                  min="5"
                  max="50"
                />
              </div>

              <div>
                <label className="block text-xs text-gray-400 mb-1">Standard Deviation</label>
                <input
                  type="number"
                  step="0.1"
                  value={config.bollingerRsiConfig?.bollingerStdDev || 2.0}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      bollingerRsiConfig: {
                        ...config.bollingerRsiConfig,
                        bollingerStdDev: parseFloat(e.target.value)
                      }
                    })
                  }
                  className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-xs"
                  min="1.0"
                  max="3.0"
                />
              </div>

              <div>
                <label className="block text-xs text-gray-400 mb-1">RSI Period</label>
                <input
                  type="number"
                  value={config.bollingerRsiConfig?.rsiPeriod || 14}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      bollingerRsiConfig: {
                        ...config.bollingerRsiConfig,
                        rsiPeriod: parseInt(e.target.value)
                      }
                    })
                  }
                  className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-xs"
                  min="5"
                  max="30"
                />
              </div>

              <div>
                <label className="block text-xs text-gray-400 mb-1">RSI Overbought</label>
                <input
                  type="number"
                  value={config.bollingerRsiConfig?.rsiOverbought || 70}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      bollingerRsiConfig: {
                        ...config.bollingerRsiConfig,
                        rsiOverbought: parseInt(e.target.value)
                      }
                    })
                  }
                  className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-xs"
                  min="60"
                  max="90"
                />
              </div>

              <div>
                <label className="block text-xs text-gray-400 mb-1">RSI Oversold</label>
                <input
                  type="number"
                  value={config.bollingerRsiConfig?.rsiOversold || 30}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      bollingerRsiConfig: {
                        ...config.bollingerRsiConfig,
                        rsiOversold: parseInt(e.target.value)
                      }
                    })
                  }
                  className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-xs"
                  min="10"
                  max="40"
                />
              </div>

              <div>
                <label className="block text-xs text-gray-400 mb-1">Volatility Threshold (%)</label>
                <input
                  type="number"
                  step="0.001"
                  value={(config.bollingerRsiConfig?.volatilityThreshold || 0.005) * 100}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      bollingerRsiConfig: {
                        ...config.bollingerRsiConfig,
                        volatilityThreshold: parseFloat(e.target.value) / 100
                      }
                    })
                  }
                  className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-xs"
                  min="0.1"
                  max="2.0"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.bollingerRsiConfig?.noiseFilter !== false}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      bollingerRsiConfig: {
                        ...config.bollingerRsiConfig,
                        noiseFilter: e.target.checked
                      }
                    })
                  }
                  className="rounded"
                />
                <span className="text-xs text-gray-300">Enable Noise Filter (M1 Optimization)</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.bollingerRsiConfig?.trendConfirmation !== false}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      bollingerRsiConfig: {
                        ...config.bollingerRsiConfig,
                        trendConfirmation: e.target.checked
                      }
                    })
                  }
                  className="rounded"
                />
                <span className="text-xs text-gray-300">Enable Trend Confirmation</span>
              </label>
            </div>
          </div>
        )

      case 'threshold':
        return (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-300">Threshold Strategy Configuration</h4>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs text-gray-400 mb-1">Threshold (%)</label>
                <input
                  type="number"
                  step="0.001"
                  value={(config.threshold || 0.02) * 100}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      threshold: parseFloat(e.target.value) / 100
                    })
                  }
                  className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-xs"
                  min="0.1"
                  max="5.0"
                />
              </div>

              <div>
                <label className="block text-xs text-gray-400 mb-1">Max Volatility (%)</label>
                <input
                  type="number"
                  step="0.1"
                  value={config.maxVolatilityPercent || 5.0}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      maxVolatilityPercent: parseFloat(e.target.value)
                    })
                  }
                  className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-xs"
                  min="1.0"
                  max="20.0"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.volatilityFilter !== false}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      volatilityFilter: e.target.checked
                    })
                  }
                  className="rounded"
                />
                <span className="text-xs text-gray-300">Enable Volatility Filter</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.momentumConfirmation !== false}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      momentumConfirmation: e.target.checked
                    })
                  }
                  className="rounded"
                />
                <span className="text-xs text-gray-300">Enable Momentum Confirmation</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.consistencyCheck !== false}
                  onChange={(e) =>
                    handleConfigUpdate(strategyType, {
                      consistencyCheck: e.target.checked
                    })
                  }
                  className="rounded"
                />
                <span className="text-xs text-gray-300">Enable Consistency Check</span>
              </label>
            </div>
          </div>
        )

      default:
        return (
          <div className="text-xs text-gray-400">
            Configuration for {strategyType} strategy is not yet implemented.
          </div>
        )
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white">Strategy Selection & Configuration</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-white">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {error && <div className="bg-red-600 text-white p-3 rounded mb-4">{error}</div>}

        {isLoading ? (
          <div className="text-center py-8">
            <div className="text-gray-400">Loading strategies...</div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Strategy Selection */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Available Strategies</h3>
              <div className="space-y-3">
                {availableStrategies.map((strategy) => (
                  <div
                    key={strategy.type}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      activeStrategies.includes(strategy.type)
                        ? 'border-green-500 bg-green-500/10'
                        : 'border-gray-600 bg-gray-700/50 hover:border-gray-500'
                    }`}
                    onClick={() => handleStrategyToggle(strategy.type)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-white">{strategy.name}</h4>
                        <p className="text-xs text-gray-400 mt-1">{strategy.description}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {activeStrategies.includes(strategy.type) && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              setSelectedStrategy(strategy.type)
                            }}
                            className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                          >
                            Configure
                          </button>
                        )}
                        <div
                          className={`w-4 h-4 rounded border-2 ${
                            activeStrategies.includes(strategy.type)
                              ? 'bg-green-500 border-green-500'
                              : 'border-gray-400'
                          }`}
                        >
                          {activeStrategies.includes(strategy.type) && (
                            <svg
                              className="w-3 h-3 text-white"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Strategy Configuration */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Strategy Configuration</h3>
              {selectedStrategy && activeStrategies.includes(selectedStrategy) ? (
                <div className="bg-gray-700/50 p-4 rounded-lg">
                  {renderStrategyConfig(selectedStrategy)}
                </div>
              ) : (
                <div className="bg-gray-700/50 p-4 rounded-lg text-center text-gray-400">
                  Select an active strategy to configure its parameters
                </div>
              )}
            </div>
          </div>
        )}

        <div className="flex justify-end mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}

export default StrategySelector
