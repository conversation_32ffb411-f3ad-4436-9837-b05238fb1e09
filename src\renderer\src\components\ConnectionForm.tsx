import { useState, useEffect } from 'react'
import { useConnectionStatus } from '../hooks/useConnectionStatus'
import { extractAuth } from '../../../shared/utils/auth'

interface ConnectionFormData {
  ssid: string
}

interface ConnectionFormProps {
  className?: string
}

interface AuthFormData {
  session: string
  uid: number
  isDemo: number
}

const ConnectionForm: React.FC<ConnectionFormProps> = ({ className = '' }) => {
  const [formData, setFormData] = useState<ConnectionFormData>({ ssid: '' })
  const [authData, setAuthData] = useState<AuthFormData>({ session: '', uid: 0, isDemo: 1 })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const connectionState = useConnectionStatus()

  // Load saved credentials on component mount
  useEffect(() => {
    const savedSSID = localStorage.getItem('trading-bot-ssid')

    if (savedSSID) {
      setFormData({
        ssid: savedSSID
      })
    }
  }, [])

  // Clear messages after a delay
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError(null)
        setSuccess(null)
      }, 5000)
      return () => clearTimeout(timer)
    }
    return undefined
  }, [error, success])

  const handleInputChange = (field: keyof ConnectionFormData, value: string): void => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }))
    // Clear error when user starts typing
    if (error) setError(null)
  }

  const validateForm = (): boolean => {
    if (!formData.ssid.trim()) {
      setError('SSID is required')
      return false
    }

    return true
  }

  const handleConnect = async (): Promise<void> => {
    if (!validateForm()) return

    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const ssID = formData.ssid.trim()
      const auth = extractAuth(ssID)

      // Send connection request to main process
      await window.api.invoke('broker:connect', {
        ssid: formData.ssid.trim(),
        isDemo: auth?.isDemo ?? 1
      })

      setAuthData(auth as AuthFormData)

      // Save credentials to localStorage for future use
      localStorage.setItem('trading-bot-ssid', formData.ssid.trim())

      setSuccess('Connection initiated successfully')
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to connect'
      setError(message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDisconnect = async (): Promise<void> => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      await window.api.invoke('broker:disconnect')
      setSuccess('Disconnected successfully')
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to disconnect'
      setError(message)
    } finally {
      setIsLoading(false)
    }
  }

  const isConnected = connectionState === 'connected' || connectionState === 'authenticated'
  const isConnecting = connectionState === 'connecting' || isLoading

  const getConnectionStatusColor = (): string => {
    switch (connectionState) {
      case 'authenticated':
        return 'text-green-400'
      case 'connected':
        return 'text-blue-400'
      case 'connecting':
        return 'text-yellow-400'
      case 'disconnected':
      default:
        return 'text-red-400'
    }
  }

  const getConnectionStatusText = (): string => {
    switch (connectionState) {
      case 'authenticated':
        return 'Authenticated'
      case 'connected':
        return 'Connected'
      case 'connecting':
        return 'Connecting...'
      case 'disconnected':
      default:
        return 'Disconnected'
    }
  }

  return (
    <div className={`bg-gray-800/80 p-2 pt-1 pb-2 rounded-xs shadow-lg w-full ${className}`}>
      {/* Header with connection status */}
      <div className="flex items-center justify-between mb-1">
        <h3 className="text-white text-md font-bold">🔗 Connection</h3>
        <div className="flex items-center space-x-2">
          <div
            className={`w-2 h-2 rounded-full ${
              connectionState === 'authenticated'
                ? 'bg-green-500'
                : connectionState === 'connected'
                  ? 'bg-green-500'
                  : connectionState === 'connecting'
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
            }`}
          ></div>
          <span className={`text-xs font-medium ${getConnectionStatusColor()}`}>
            {getConnectionStatusText()}
          </span>
        </div>
      </div>

      <div
        className={`flex flex-${connectionState === 'authenticated' ? 'row' : 'col'} items-end w-full gap-2`}
      >
        {/* Connection Info when connected */}
        {isConnected && (
          <div className="flex-1 bg-green-600/20 border border-green-600/50 rounded-xs p-2">
            <h4 className="text-green-400 text-sm font-medium mb-2">Connected Account</h4>
            <div className="space-y-1">
              <div className="flex gap-4 text-xs text-gray-300">
                <span className="text-gray-400">
                  Session: <span className="text-white">{formData.ssid.substring(0, 20)}...</span>
                </span>
                <span className="text-gray-400">
                  Account Type:{' '}
                  <span
                    className={`text-white p-1 bg-${authData.isDemo === 1 ? 'amber' : 'green'}-600/50 rounded-sm`}
                  >
                    {authData.isDemo === 1 ? 'Demo' : 'Real'}
                  </span>
                </span>
              </div>
            </div>
          </div>
        )}

        {/* SSID Input */}
        {!isConnected && (
          <div className="flex flex-col w-full">
            <input
              type="text"
              value={formData.ssid}
              onChange={(e) => handleInputChange('ssid', e.target.value)}
              className="w-full px-2 py-1 bg-gray-700 border text-gray-400 border-gray-600 rounded-xs text-sm focus:outline-none focus:ring-1 focus:ring-blue-500/50 focus:border-transparent"
              placeholder="Enter your session ID"
              disabled={isConnected || isConnecting}
            />
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-600/20 border border-red-600/50 rounded-xs p-2">
            <p className="text-red-400 text-xs">{error}</p>
          </div>
        )}

        {/* Connect/Disconnect Button */}
        <div className="flex justify-end w-1/5">
          <button
            onClick={isConnected ? handleDisconnect : handleConnect}
            disabled={isConnecting || (!isConnected && !formData.ssid.trim())}
            className={`px-2 py-1 rounded-xs font-bold text-sm text-white transition-colors ${
              isConnected ? 'bg-red-600 hover:bg-red-700' : 'bg-blue-600 hover:bg-blue-700'
            } ${
              isConnecting || (!isConnected && !formData.ssid.trim())
                ? 'opacity-50 cursor-not-allowed'
                : 'cursor-pointer'
            }`}
          >
            {isConnecting ? 'Processing...' : isConnected ? 'Disconnect' : 'Connect'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default ConnectionForm
