import { ThresholdStrategy } from '../strategies/ThresholdStrategy'
import { SuperTrendStrategy } from '../strategies/SuperTrendStrategy'
import { TradingStrategy } from '../strategies/TradingStrategy'
import { Back2TrendStrategy } from '../strategies/Back2TrendStrategy'
import { BollingerRSIStrategy } from '../strategies/BollingerRSIStrategy'

export class StrategyFactory {
  static createStrategy(type: StrategyType, config: StrategyConfig): TradingStrategy {
    switch (type) {
      case 'threshold':
        return new ThresholdStrategy(config)
      case 'supertrend':
        return new SuperTrendStrategy(config)
      case 'back2trend':
        return new Back2TrendStrategy(config)
      case 'bollinger-rsi':
        return new BollingerRSIStrategy(config)
      default:
        throw new Error(`Strategy ${type} not found`)
    }
  }

  static getAvailableStrategies(): Array<{
    type: StrategyType
    name: string
    description: string
  }> {
    return [
      {
        type: 'threshold',
        name: 'Threshold Strategy',
        description:
          'Advanced threshold strategy with momentum analysis, volatility filtering, and trend consistency checks.'
      },
      {
        type: 'supertrend',
        name: 'SuperTrend Strategy',
        description:
          'Multi-timeframe SuperTrend strategy that generates signals when all SuperTrend indicators align in the same direction.'
      },
      {
        type: 'back2trend',
        name: 'Back 2 Trend Strategy',
        description:
          'Enters on a pullback against a strong trend after consolidation and breakout of the trendline.'
      },
      {
        type: 'bollinger-rsi',
        name: 'Bollinger Bands + RSI Strategy',
        description:
          'Advanced strategy combining Bollinger Bands and RSI indicators, optimized for M1 chart analysis with noise filtering and trend confirmation.'
      }
    ]
  }

  static getDefaultConfig(type: StrategyType): StrategyConfig {
    switch (type) {
      case 'threshold':
        return {
          threshold: 0.02,
          minConfidence: 0.4,
          volatilityFilter: true,
          momentumConfirmation: true,
          consistencyCheck: true
        }
      case 'supertrend':
        return {
          minConfidence: 0.6,
          superTrends: [
            { atrPeriod: 10, multiplier: 3.0 },
            { atrPeriod: 14, multiplier: 2.0 },
            { atrPeriod: 21, multiplier: 1.5 }
          ]
        }
      case 'bollinger-rsi':
        return {
          minConfidence: 0.5,
          bollingerRsiConfig: {
            bollingerPeriod: 20,
            bollingerStdDev: 2.0,
            rsiPeriod: 9,
            rsiOverbought: 85,
            rsiOversold: 15,
            noiseFilter: true,
            trendConfirmation: true,
            volatilityThreshold: 0.005 // 0.5% for M1 optimization
          }
        }
      // Add more cases for other strategies
      default:
        throw new Error(`Strategy ${type} not found`)
    }
  }
}
