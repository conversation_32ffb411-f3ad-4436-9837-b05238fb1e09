import { SignalEngine } from '../main/core/SignalEngine'
import { StrategyFactory } from '../main/core/StrategyFactory'

/**
 * Test Dynamic Strategy Switching Functionality
 * 
 * This test suite verifies that:
 * 1. Strategies can be switched at runtime without data loss
 * 2. Strategy configurations are properly updated
 * 3. Historical data is maintained across strategy switches
 * 4. Multiple strategies can be active simultaneously
 * 5. Strategy instances are properly cleaned up when deactivated
 */

// Mock candle data generator
const generateMockCandles = (count: number, basePrice: number = 1.0000): Candle[] => {
  const candles: Candle[] = []
  let price = basePrice
  
  for (let i = 0; i < count; i++) {
    const change = (Math.random() - 0.5) * 0.0002
    price += change
    
    const high = price + Math.random() * 0.0001
    const low = price - Math.random() * 0.0001
    const open = i === 0 ? price : candles[i - 1].close
    
    candles.push({
      time: Date.now() + i * 60000, // 1 minute intervals
      open,
      high,
      low,
      close: price
    })
  }
  
  return candles
}

async function testDynamicStrategySwitching() {
  console.log('🧪 Testing Dynamic Strategy Switching...\n')

  // Test 1: Initialize SignalEngine with default strategy
  console.log('Test 1: Initialize SignalEngine with default strategy')
  
  const initialStrategies = [{ name: 'threshold' }]
  const initialSettings: TradeSettings = {
    confidenceThreshold: 0.5,
    candlePeriodSeconds: 60,
    expirySeconds: 60,
    activeStrategies: ['threshold']
  }
  
  const signalEngine = new SignalEngine(initialStrategies, initialSettings)
  
  console.log(`✓ Initial active strategies: ${signalEngine.getActiveStrategies().join(', ')}`)
  console.log(`✓ Strategy configs loaded: ${Object.keys(signalEngine.getStrategyConfigs()).join(', ')}\n`)

  // Test 2: Add candles to build history
  console.log('Test 2: Build strategy history with candles')
  
  const testCandles = generateMockCandles(30)
  
  for (const candle of testCandles) {
    await signalEngine.generate(candle)
  }
  
  console.log(`✓ Processed ${testCandles.length} candles`)
  console.log(`✓ Candle count: ${signalEngine.getCandleCount()}\n`)

  // Test 3: Switch to different strategy
  console.log('Test 3: Switch to Bollinger RSI strategy')
  
  try {
    signalEngine.updateStrategies(['bollinger-rsi'])
    
    console.log(`✓ Active strategies updated: ${signalEngine.getActiveStrategies().join(', ')}`)
    
    // Process more candles with new strategy
    const newCandles = generateMockCandles(10, testCandles[testCandles.length - 1].close)
    
    for (const candle of newCandles) {
      await signalEngine.generate(candle)
    }
    
    console.log(`✓ Processed ${newCandles.length} additional candles with new strategy`)
    console.log(`✓ Total candle count maintained: ${signalEngine.getCandleCount()}\n`)
  } catch (error) {
    console.error(`✗ Failed to switch strategy: ${error}`)
  }

  // Test 4: Multiple active strategies
  console.log('Test 4: Activate multiple strategies simultaneously')
  
  try {
    signalEngine.updateStrategies(['threshold', 'bollinger-rsi', 'supertrend'])
    
    console.log(`✓ Multiple strategies activated: ${signalEngine.getActiveStrategies().join(', ')}`)
    
    // Test signal generation with multiple strategies
    const multiStrategyCandle = generateMockCandles(1, testCandles[testCandles.length - 1].close)[0]
    const signals = await signalEngine.generate(multiStrategyCandle)
    
    console.log(`✓ Generated ${signals.length} signals from multiple strategies`)
    
    if (signals.length > 0) {
      console.log(`✓ Best signal: ${signalEngine.getBestSignal().action} (confidence: ${(signalEngine.getBestSignal().confidence * 100).toFixed(1)}%)`)
    }
    console.log()
  } catch (error) {
    console.error(`✗ Failed to activate multiple strategies: ${error}`)
  }

  // Test 5: Update strategy configuration
  console.log('Test 5: Update strategy configuration dynamically')
  
  try {
    const originalConfig = signalEngine.getStrategyConfigs()['bollinger-rsi']
    console.log(`✓ Original Bollinger RSI config: RSI Period = ${originalConfig?.bollingerRsiConfig?.rsiPeriod || 'N/A'}`)
    
    // Update RSI period
    signalEngine.updateStrategyConfig('bollinger-rsi', {
      bollingerRsiConfig: {
        ...originalConfig?.bollingerRsiConfig,
        rsiPeriod: 21,
        rsiOverbought: 75,
        rsiOversold: 25
      }
    })
    
    const updatedConfig = signalEngine.getStrategyConfigs()['bollinger-rsi']
    console.log(`✓ Updated Bollinger RSI config: RSI Period = ${updatedConfig?.bollingerRsiConfig?.rsiPeriod}`)
    console.log(`✓ Updated thresholds: Overbought = ${updatedConfig?.bollingerRsiConfig?.rsiOverbought}, Oversold = ${updatedConfig?.bollingerRsiConfig?.rsiOversold}`)
    
    // Test that updated config is used
    const configTestCandle = generateMockCandles(1, testCandles[testCandles.length - 1].close)[0]
    await signalEngine.generate(configTestCandle)
    
    console.log(`✓ Strategy successfully used updated configuration\n`)
  } catch (error) {
    console.error(`✗ Failed to update strategy configuration: ${error}`)
  }

  // Test 6: Strategy removal and cleanup
  console.log('Test 6: Remove strategies and verify cleanup')
  
  try {
    const beforeConfigs = Object.keys(signalEngine.getStrategyConfigs())
    console.log(`✓ Configs before removal: ${beforeConfigs.join(', ')}`)
    
    // Remove all but one strategy
    signalEngine.updateStrategies(['bollinger-rsi'])
    
    const afterStrategies = signalEngine.getActiveStrategies()
    const afterConfigs = Object.keys(signalEngine.getStrategyConfigs())
    
    console.log(`✓ Active strategies after removal: ${afterStrategies.join(', ')}`)
    console.log(`✓ Remaining configs: ${afterConfigs.join(', ')}`)
    
    // Verify the remaining strategy still works
    const finalCandle = generateMockCandles(1, testCandles[testCandles.length - 1].close)[0]
    const finalSignals = await signalEngine.generate(finalCandle)
    
    console.log(`✓ Final strategy test: Generated ${finalSignals.length} signals`)
    console.log(`✓ Strategy cleanup completed successfully\n`)
  } catch (error) {
    console.error(`✗ Failed strategy removal test: ${error}`)
  }

  // Test 7: Error handling
  console.log('Test 7: Error handling for invalid operations')
  
  try {
    // Try to update config for inactive strategy
    try {
      signalEngine.updateStrategyConfig('threshold', { minConfidence: 0.8 })
      console.log(`✗ Should have thrown error for inactive strategy`)
    } catch (error) {
      console.log(`✓ Correctly threw error for inactive strategy: ${error}`)
    }
    
    // Try to activate invalid strategy
    try {
      signalEngine.updateStrategies(['invalid-strategy' as StrategyType])
      console.log(`✗ Should have thrown error for invalid strategy`)
    } catch (error) {
      console.log(`✓ Correctly handled invalid strategy: ${error}`)
    }
    
    console.log(`✓ Error handling tests completed\n`)
  } catch (error) {
    console.error(`✗ Unexpected error in error handling tests: ${error}`)
  }

  // Test 8: Performance test
  console.log('Test 8: Performance test with rapid strategy switching')
  
  try {
    const startTime = Date.now()
    
    // Rapidly switch between strategies
    for (let i = 0; i < 5; i++) {
      signalEngine.updateStrategies(['threshold'])
      signalEngine.updateStrategies(['bollinger-rsi'])
      signalEngine.updateStrategies(['supertrend'])
      
      // Process a candle with each switch
      const perfCandle = generateMockCandles(1, 1.0000)[0]
      await signalEngine.generate(perfCandle)
    }
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    console.log(`✓ Rapid switching test completed in ${duration}ms`)
    console.log(`✓ Final active strategy: ${signalEngine.getActiveStrategies().join(', ')}\n`)
  } catch (error) {
    console.error(`✗ Performance test failed: ${error}`)
  }

  console.log('✅ Dynamic Strategy Switching tests completed!')
  console.log('\n📊 Test Summary:')
  console.log('- ✓ Strategy initialization and configuration')
  console.log('- ✓ Runtime strategy switching')
  console.log('- ✓ Historical data preservation')
  console.log('- ✓ Multiple strategy support')
  console.log('- ✓ Dynamic configuration updates')
  console.log('- ✓ Strategy cleanup and removal')
  console.log('- ✓ Error handling')
  console.log('- ✓ Performance under rapid switching')
}

// Run the test if this file is executed directly
if (require.main === module) {
  testDynamicStrategySwitching().catch(console.error)
}

export { testDynamicStrategySwitching }
