import { TradingStrategy } from '../strategies/TradingStrategy'
import { StrategyFactory } from './StrategyFactory'
// import { Logger } from '../../shared/utils/Logger'

// const logger = Logger.getContextLogger('SIGNAL')

export class SignalEngine {
  private tradeSettings: TradeSettings = {
    confidenceThreshold: 0.1, // Temporarily lowered for testing
    profitMargin: 0.005,
    riskTolerance: 0.002,
    leverage: 1,
    stopLoss: 0.05,
    takeProfit: 0.1,
    // Default to threshold strategy for backward compatibility
    activeStrategies: ['threshold'],
    primaryStrategy: 'threshold'
  }

  private currentSignal: Signal | null = null
  private candleCount: number = 0
  private strategyInstances: Record<string, TradingStrategy> = {}
  private strategyConfigs: Partial<Record<StrategyType, StrategyConfig>> = {}

  constructor(strategies: Strategy[], tradeSettings: TradeSettings) {
    // Extract strategy types from legacy Strategy[] format for backward compatibility
    const strategyTypes = strategies.map((s) => s.name as StrategyType)

    this.tradeSettings = {
      ...this.tradeSettings,
      ...tradeSettings,
      // Use provided strategies or fall back to legacy format
      activeStrategies: tradeSettings.activeStrategies || strategyTypes,
      // Ensure confidenceThreshold is always set to our default if not provided
      confidenceThreshold:
        tradeSettings.confidenceThreshold ?? this.tradeSettings.confidenceThreshold
    }

    // Initialize strategy configurations
    this.initializeStrategyConfigs()
  }

  /**
   * Initialize strategy configurations with defaults or user-provided configs
   */
  private initializeStrategyConfigs(): void {
    const activeStrategies = this.tradeSettings.activeStrategies || ['threshold']
    const userConfigs = this.tradeSettings.strategyConfigs || {}

    // Initialize configs for active strategies
    for (const strategyType of activeStrategies) {
      this.strategyConfigs[strategyType] = {
        ...StrategyFactory.getDefaultConfig(strategyType),
        ...userConfigs[strategyType],
        // Always include common settings from tradeSettings
        minConfidence: this.tradeSettings.confidenceThreshold,
        candlePeriodSeconds: this.tradeSettings.candlePeriodSeconds,
        expiryToSeconds: this.tradeSettings.expirySeconds
      }
    }
  }

  /**
   * Update active strategies and their configurations
   */
  public updateStrategies(
    activeStrategies: StrategyType[],
    strategyConfigs?: Partial<Record<StrategyType, StrategyConfig>>
  ): void {
    // Update trade settings
    this.tradeSettings.activeStrategies = activeStrategies
    if (strategyConfigs) {
      this.tradeSettings.strategyConfigs = {
        ...this.tradeSettings.strategyConfigs,
        ...strategyConfigs
      }
    }

    // Clear old strategy instances that are no longer active
    const activeStrategyNames = new Set(activeStrategies)
    Object.keys(this.strategyInstances).forEach((name) => {
      if (!activeStrategyNames.has(name as StrategyType)) {
        delete this.strategyInstances[name]
      }
    })

    // Reinitialize strategy configurations
    this.initializeStrategyConfigs()
  }

  /**
   * Update configuration for a specific strategy
   */
  public updateStrategyConfig(
    strategyType: StrategyType,
    configUpdate: Partial<StrategyConfig>
  ): void {
    if (!this.tradeSettings.activeStrategies?.includes(strategyType)) {
      throw new Error(`Strategy ${strategyType} is not currently active`)
    }

    // Update the stored configuration
    this.strategyConfigs[strategyType] = {
      ...this.strategyConfigs[strategyType],
      ...configUpdate
    }

    // Update trade settings
    if (!this.tradeSettings.strategyConfigs) {
      this.tradeSettings.strategyConfigs = {}
    }
    const updatedConfig = this.strategyConfigs[strategyType]
    if (updatedConfig) {
      this.tradeSettings.strategyConfigs[strategyType] = updatedConfig
    }

    // Clear the strategy instance to force recreation with new config
    delete this.strategyInstances[strategyType]
  }

  /**
   * Get current strategy configurations
   */
  public getStrategyConfigs(): Partial<Record<StrategyType, StrategyConfig>> {
    return { ...this.strategyConfigs }
  }

  /**
   * Get list of active strategies
   */
  public getActiveStrategies(): StrategyType[] {
    return this.tradeSettings.activeStrategies || ['threshold']
  }

  /**
   * Create a strategy instance using stored configuration
   */
  private createStrategyInstance(strategyType: StrategyType): TradingStrategy {
    const config = this.strategyConfigs[strategyType]
    if (!config) {
      throw new Error(`No configuration found for strategy: ${strategyType}`)
    }

    return StrategyFactory.createStrategy(strategyType, config)
  }

  async generate(candle: Candle): Promise<Signal[]> {
    const activeStrategyTypes = this.getActiveStrategies()
    const strategies = activeStrategyTypes.map((type) => ({ name: type }))
    const results = await Promise.all(
      strategies.map((strategy) => this.runStrategy(strategy, candle))
    )
    const signals = results.filter((s): s is Signal => s !== null)

    const finalSignal = this.combineSignalsByVoting(signals)

    const confThreshold = this.tradeSettings.confidenceThreshold ?? 0.4
    if (finalSignal.confidence < confThreshold) {
      finalSignal.action = 'HOLD'
      finalSignal.reason = `Confidence ${Math.round(finalSignal.confidence * 100)}% is below minimum ${confThreshold * 100}%`
      // logger.debug(
      //   `Signal blocked: confidence ${(finalSignal.confidence * 100).toFixed(1)}% < threshold ${(confThreshold * 100).toFixed(1)}%`
      // )
    }

    this.currentSignal = finalSignal
    return signals
  }

  getBestSignal(): Signal {
    return (
      this.currentSignal ?? {
        shouldTrade: false,
        action: 'HOLD',
        reason: 'No signal generated',
        confidence: 0
      }
    )
  }

  getCandleCount(): number {
    return this.candleCount
  }

  private async runStrategy(strategy: Strategy, candle: Candle): Promise<Signal | null> {
    const strategyType = strategy.name as StrategyType

    // Check if strategy is supported
    if (!this.strategyConfigs[strategyType]) {
      return null
    }

    const strategyInstance = this.getStrategyInstance(strategyType)
    const decision = await strategyInstance.evaluate(candle)

    this.candleCount = strategyInstance.getCandleCount()

    let action: 'BUY' | 'SELL' | 'HOLD' = 'HOLD'
    if (decision.direction === 'high') action = 'BUY'
    else if (decision.direction === 'low') action = 'SELL'

    return {
      shouldTrade: decision.shouldTrade,
      action,
      reason: decision.reason ?? '',
      confidence: decision.confidence ?? 0,
      expirySeconds: decision.expirySeconds
    }
  }

  // @ts-ignore - Method reserved for future use
  private combineSignals(signals: Signal[]): Signal {
    // 1. Handle the case where no signals are provided.
    if (!signals.length) {
      return {
        shouldTrade: false,
        action: 'HOLD',
        reason: 'No signal generated',
        confidence: 0
      }
    }

    // 2. Filter for actionable signals ('BUY' or 'SELL').
    const tradingSignals = signals.filter(
      (signal) => signal.action === 'BUY' || signal.action === 'SELL'
    )

    // 3. If there are any actionable signals, find the one with the highest confidence among them.
    if (tradingSignals.length > 0) {
      return tradingSignals.reduce((best, current) => {
        return current.confidence > best.confidence ? current : best
      })
    }

    // 4. If no actionable signals were found, return the highest-confidence 'HOLD' signal.
    return signals.reduce((best, current) => {
      return current.confidence > best.confidence ? current : best
    })
  }

  // public combine(signals: Signal[]): Signal {
  //   if (!signals.length) {
  //     return {
  //       shouldTrade: false,
  //       action: 'HOLD',
  //       reason: 'No signal generated',
  //       confidence: 0
  //     }
  //   }

  //   const votes: Record<'BUY' | 'SELL' | 'HOLD', number> = {
  //     BUY: 0,
  //     SELL: 0,
  //     HOLD: 0
  //   }

  //   signals.forEach((signal) => {
  //     votes[signal.action]++
  //   })

  //   let winningAction: 'BUY' | 'SELL' | 'HOLD' = 'HOLD'
  //   let maxVotes = 0

  //   Object.entries(votes).forEach(([action, count]) => {
  //     if (count > maxVotes) {
  //       maxVotes = count
  //       winningAction = action as 'BUY' | 'SELL' | 'HOLD'
  //     }
  //   })

  //   const actionsWithMaxVotes = Object.entries(votes)
  //     .filter(([, count]) => count === maxVotes)
  //     .map(([action]) => action as 'BUY' | 'SELL' | 'HOLD')

  //   if (actionsWithMaxVotes.length > 0) {
  //     let bestAction = actionsWithMaxVotes[0]

  //     actionsWithMaxVotes.forEach((action) => {
  //       const actionSignals = signals.filter((signal) => signal.action === action)

  //       logger.debug(
  //         `${action} signals: ${actionSignals.length}`,
  //         JSON.stringify(actionSignals, null, 2)
  //       )
  //     })
  //   }
  // }

  private combineSignalsByVoting(signals: Signal[]): Signal {
    // 1. Handle the case where no signals are provided.
    if (!signals.length) {
      return {
        shouldTrade: false,
        action: 'HOLD',
        reason: 'No signal generated',
        confidence: 0
      }
    }

    // 2. Count the votes for each action.
    const voteCounts: Record<'BUY' | 'SELL' | 'HOLD', number> = {
      BUY: 0,
      SELL: 0,
      HOLD: 0
    }

    signals.forEach((signal) => {
      voteCounts[signal.action]++
    })

    // 3. Determine the winning action by majority vote.
    let winningAction: 'BUY' | 'SELL' | 'HOLD' = 'HOLD'
    let maxVotes = 0

    // Find the action with the most votes
    Object.entries(voteCounts).forEach(([action, count]) => {
      if (count > maxVotes) {
        maxVotes = count
        winningAction = action as 'BUY' | 'SELL' | 'HOLD'
      }
    })

    // 4. Handle ties by using confidence levels
    const actionsWithMaxVotes = Object.entries(voteCounts)
      .filter(([, count]) => count === maxVotes)
      .map(([action]) => action as 'BUY' | 'SELL' | 'HOLD')

    if (actionsWithMaxVotes.length > 1) {
      // Tie-breaking: find the action with highest confidence among tied actions
      let bestAction = actionsWithMaxVotes[0]
      let bestConfidence = 0

      actionsWithMaxVotes.forEach((action) => {
        const actionSignals = signals.filter((signal) => signal.action === action)
        const avgConfidence =
          actionSignals.reduce((sum, signal) => sum + signal.confidence, 0) / actionSignals.length

        if (avgConfidence > bestConfidence) {
          bestConfidence = avgConfidence
          bestAction = action
        }
      })

      winningAction = bestAction
    }

    // 5. Get all signals for the winning action
    const winningSignals = signals.filter((signal) => signal.action === winningAction)

    // 6. Calculate combined confidence and create result
    const avgConfidence =
      winningSignals.reduce((sum, signal) => sum + signal.confidence, 0) / winningSignals.length

    // Find the signal with highest confidence for the reason
    const bestSignal = winningSignals.reduce((best, current) =>
      current.confidence > best.confidence ? current : best
    )

    return {
      shouldTrade: winningAction !== 'HOLD',
      action: winningAction,
      reason: `${winningAction} signal won by ${maxVotes}/${signals.length} votes. ${bestSignal.reason}`,
      confidence: avgConfidence
    }
  }

  private getStrategyInstance(strategyType: StrategyType): TradingStrategy {
    if (!this.strategyInstances[strategyType]) {
      this.strategyInstances[strategyType] = this.createStrategyInstance(strategyType)
    }

    return this.strategyInstances[strategyType]
  }
}
