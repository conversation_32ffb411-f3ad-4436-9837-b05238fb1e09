import { TradingStrategy } from './TradingStrategy'

interface BollingerBands {
  upper: number
  middle: number // SMA
  lower: number
  bandwidth: number // (upper - lower) / middle
  percentB: number // (price - lower) / (upper - lower)
}

interface BollingerRSISignal {
  type: 'buy' | 'sell' | 'hold'
  strength: number // 0-1 scale
  reason: string
  bollingerSignal: string
  rsiSignal: string
  trendAlignment: boolean
  noiseFiltered: boolean
}

/**
 * Bollinger Bands + RSI Strategy optimized for M1 (1-minute) chart analysis
 *
 * This strategy combines Bollinger Bands and RSI indicators to generate high-quality
 * trading signals while filtering out noise typical in 1-minute timeframes.
 *
 * Entry Conditions:
 * - BUY: Price touches lower Bollinger Band + RSI oversold (< 30) + trend confirmation
 * - SELL: Price touches upper Bollinger Band + RSI overbought (> 70) + trend confirmation
 *
 * M1 Optimizations:
 * - Noise filtering using volatility thresholds
 * - Trend confirmation using SMA direction
 * - Enhanced confidence scoring for short-term movements
 * - False signal reduction through multiple confirmations
 */
export class BollingerRSIStrategy extends TradingStrategy {
  private readonly bollingerPeriod: number
  private readonly bollingerStdDev: number
  private readonly rsiPeriod: number
  private readonly rsiOverbought: number
  private readonly rsiOversold: number
  private readonly noiseFilter: boolean
  private readonly trendConfirmation: boolean
  private readonly volatilityThreshold: number

  constructor(config: StrategyConfig) {
    super(config)

    const bollingerConfig = config.bollingerRsiConfig || {}
    this.bollingerPeriod = bollingerConfig.bollingerPeriod || 20
    this.bollingerStdDev = bollingerConfig.bollingerStdDev || 2.0
    this.rsiPeriod = bollingerConfig.rsiPeriod || 14
    this.rsiOverbought = bollingerConfig.rsiOverbought || 70
    this.rsiOversold = bollingerConfig.rsiOversold || 30
    this.noiseFilter = bollingerConfig.noiseFilter !== false // default true
    this.trendConfirmation = bollingerConfig.trendConfirmation !== false // default true
    this.volatilityThreshold = bollingerConfig.volatilityThreshold || 0.005 // 0.5% for M1
  }

  getName(): string {
    return 'Bollinger Bands + RSI Strategy'
  }

  getDescription(): string {
    return 'Advanced strategy combining Bollinger Bands and RSI indicators, optimized for M1 chart analysis with noise filtering and trend confirmation.'
  }

  getCandleCount(): number {
    return this.priceHistory.length
  }

  async evaluate(candle: Candle): Promise<TradingDecision> {
    this.addCandle(candle)

    // Need enough data for both indicators
    const requiredCandles = Math.max(this.bollingerPeriod, this.rsiPeriod) + 5
    if (this.priceHistory.length < requiredCandles) {
      return {
        shouldTrade: false,
        reason: `Insufficient data: need ${requiredCandles} candles, have ${this.priceHistory.length}`,
        confidence: 0
      }
    }

    // Sort price history by time
    this.priceHistory.sort((a, b) => a.time - b.time)

    // Calculate indicators
    const bollingerBands = this.calculateBollingerBands()
    const rsiResult = this.calculateRSI(this.rsiPeriod)

    if (!bollingerBands) {
      return {
        shouldTrade: false,
        reason: 'Unable to calculate Bollinger Bands',
        confidence: 0
      }
    }

    // Generate signal
    const signal = this.generateSignal(candle, bollingerBands, rsiResult)

    // Apply M1 optimizations and filters
    const filteredSignal = this.applyM1Filters(signal, bollingerBands)

    return this.convertToTradingDecision(filteredSignal)
  }

  /**
   * Calculate Bollinger Bands with configurable period and standard deviation
   */
  private calculateBollingerBands(): BollingerBands | null {
    if (this.priceHistory.length < this.bollingerPeriod) {
      return null
    }

    // Get closing prices for the period
    const closes = this.priceHistory.slice(-this.bollingerPeriod).map((c) => c.close)

    // Calculate Simple Moving Average (middle band)
    const sma = closes.reduce((sum, price) => sum + price, 0) / closes.length

    // Calculate standard deviation
    const variance =
      closes.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / closes.length
    const stdDev = Math.sqrt(variance)

    // Calculate bands
    const upper = sma + this.bollingerStdDev * stdDev
    const lower = sma - this.bollingerStdDev * stdDev

    const currentPrice = this.priceHistory[this.priceHistory.length - 1].close
    const bandwidth = (upper - lower) / sma
    const percentB = (currentPrice - lower) / (upper - lower)

    return {
      upper,
      middle: sma,
      lower,
      bandwidth,
      percentB
    }
  }

  /**
   * Generate initial signal based on Bollinger Bands and RSI
   */
  private generateSignal(candle: Candle, bb: BollingerBands, rsi: RSIResult): BollingerRSISignal {
    const price = candle.close
    let type: 'buy' | 'sell' | 'hold' = 'hold'
    let strength = 0
    let reason = ''
    let bollingerSignal = ''
    let rsiSignal = ''

    // Bollinger Bands analysis
    const touchingLower = price <= bb.lower * 1.001 // 0.1% tolerance
    const touchingUpper = price >= bb.upper * 0.999 // 0.1% tolerance
    const nearLower = bb.percentB < 0.2
    const nearUpper = bb.percentB > 0.8

    if (touchingLower || nearLower) {
      bollingerSignal = touchingLower ? 'touching_lower' : 'near_lower'
    } else if (touchingUpper || nearUpper) {
      bollingerSignal = touchingUpper ? 'touching_upper' : 'near_upper'
    } else {
      bollingerSignal = 'middle_range'
    }

    // RSI analysis
    rsiSignal = rsi.trend

    // Generate signals using configurable thresholds
    const isRSIOversold = rsi.rsi < this.rsiOversold
    const isRSIOverbought = rsi.rsi > this.rsiOverbought

    if ((touchingLower || nearLower) && isRSIOversold) {
      type = 'buy'
      strength = touchingLower ? 0.8 : 0.6
      reason = `Price ${touchingLower ? 'touching' : 'near'} lower BB (${bb.lower.toFixed(5)}) + RSI oversold (${rsi.rsi.toFixed(1)} < ${this.rsiOversold})`
    } else if ((touchingUpper || nearUpper) && isRSIOverbought) {
      type = 'sell'
      strength = touchingUpper ? 0.8 : 0.6
      reason = `Price ${touchingUpper ? 'touching' : 'near'} upper BB (${bb.upper.toFixed(5)}) + RSI overbought (${rsi.rsi.toFixed(1)} > ${this.rsiOverbought})`
    } else {
      reason = `No clear signal: BB=${bollingerSignal}, RSI=${rsi.trend} (${rsi.rsi.toFixed(1)})`
    }

    return {
      type,
      strength,
      reason,
      bollingerSignal,
      rsiSignal,
      trendAlignment: false, // Will be calculated in filters
      noiseFiltered: false
    }
  }

  /**
   * Apply M1-specific filters and optimizations
   */
  private applyM1Filters(signal: BollingerRSISignal, bb: BollingerBands): BollingerRSISignal {
    let filteredSignal = { ...signal }

    // 1. Trend confirmation using SMA direction
    if (this.trendConfirmation && signal.type !== 'hold') {
      const trendAlignment = this.checkTrendAlignment(signal.type, bb)
      filteredSignal.trendAlignment = trendAlignment

      if (trendAlignment) {
        filteredSignal.strength += 0.2
        filteredSignal.reason += ' + Trend aligned'
      } else {
        filteredSignal.strength -= 0.3
        filteredSignal.reason += ' - Against trend'
      }
    }

    // 2. Noise filtering for M1 volatility
    if (this.noiseFilter) {
      const isNoisy = this.isMarketNoisy(bb)
      filteredSignal.noiseFiltered = !isNoisy

      if (isNoisy) {
        filteredSignal.strength -= 0.4
        filteredSignal.reason += ' - High noise detected'
      } else {
        filteredSignal.strength += 0.1
        filteredSignal.reason += ' + Low noise'
      }
    }

    // 3. Volatility threshold check
    if (bb.bandwidth > this.volatilityThreshold) {
      filteredSignal.strength -= 0.2
      filteredSignal.reason += ' - High volatility'
    }

    // 4. Ensure strength stays within bounds
    filteredSignal.strength = Math.max(0, Math.min(1, filteredSignal.strength))

    // 5. Override signal if strength is too low
    if (filteredSignal.strength < 0.3) {
      filteredSignal.type = 'hold'
      filteredSignal.reason += ' - Low confidence after filtering'
    }

    return filteredSignal
  }

  /**
   * Check if the signal aligns with the current trend
   */
  private checkTrendAlignment(signalType: 'buy' | 'sell', bb: BollingerBands): boolean {
    if (this.priceHistory.length < 10) return false

    // Use recent price action relative to middle band (SMA)
    const recentCandles = this.priceHistory.slice(-5)
    const avgPrice = recentCandles.reduce((sum, c) => sum + c.close, 0) / recentCandles.length

    const isUptrend = avgPrice > bb.middle
    const isDowntrend = avgPrice < bb.middle

    return (signalType === 'buy' && isDowntrend) || (signalType === 'sell' && isUptrend)
  }

  /**
   * Detect if market is too noisy for reliable signals (M1 optimization)
   */
  private isMarketNoisy(bb: BollingerBands): boolean {
    if (this.priceHistory.length < 10) return true

    // Check for excessive price swings in recent candles
    const recentCandles = this.priceHistory.slice(-5)
    let swingCount = 0

    for (let i = 1; i < recentCandles.length; i++) {
      const prevChange =
        recentCandles[i - 1].close -
        (i > 1 ? recentCandles[i - 2].close : recentCandles[i - 1].open)
      const currentChange = recentCandles[i].close - recentCandles[i - 1].close

      // Count direction changes
      if ((prevChange > 0 && currentChange < 0) || (prevChange < 0 && currentChange > 0)) {
        swingCount++
      }
    }

    // Also check bandwidth - very tight bands indicate low volatility/noise
    const isHighNoise = swingCount >= 3 || bb.bandwidth > this.volatilityThreshold * 2

    return isHighNoise
  }

  /**
   * Convert internal signal to TradingDecision
   */
  private convertToTradingDecision(signal: BollingerRSISignal): TradingDecision {
    if (signal.type === 'hold') {
      return {
        shouldTrade: false,
        reason: signal.reason,
        confidence: signal.strength
      }
    }

    const direction = signal.type === 'buy' ? 'high' : 'low'
    const minConfidence = this.config.minConfidence || 0.5

    const shouldTrade = signal.strength >= minConfidence

    // Calculate dynamic expiry based on signal strength and M1 characteristics
    const baseExpiry = this.config.expiryToSeconds || 60 // Default 1 minute for M1
    const expiryMultiplier = Math.max(0.5, Math.min(2.0, signal.strength * 2))
    const expirySeconds = Math.round(baseExpiry * expiryMultiplier)

    return {
      shouldTrade,
      direction,
      confidence: signal.strength,
      reason: signal.reason,
      expirySeconds,
      metadata: {
        bollingerSignal: signal.bollingerSignal,
        rsiSignal: signal.rsiSignal,
        trendAlignment: signal.trendAlignment,
        noiseFiltered: signal.noiseFiltered
      }
    }
  }
}
